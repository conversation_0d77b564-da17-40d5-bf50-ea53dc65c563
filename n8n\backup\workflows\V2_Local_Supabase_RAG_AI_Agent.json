{"name": "V2 Supabase RAG AI Agent", "nodes": [{"parameters": {}, "id": "3e70b57d-49fb-4cb0-8f9f-29d39adf6a65", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.1, "position": [480, 340], "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"model": "qwen2.5:7b-instruct-q4_K_M", "options": {}}, "id": "8d61de27-45d8-4d10-97cc-3c36d224f865", "name": "<PERSON><PERSON><PERSON> Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [360, 340], "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"model": "qwen2.5:7b-instruct-q4_K_M", "options": {}}, "id": "cbd5b56f-7afc-4e83-a221-6be4d348374e", "name": "Ollama Model", "type": "@n8n/n8n-nodes-langchain.lmOllama", "typeVersion": 1, "position": [1400, 280], "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"name": "documents", "topK": 3}, "id": "c15bd0a8-286a-4076-8ce8-8c0e54f73e2a", "name": "Vector Store Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [1180, 100]}, {"parameters": {"operation": "text", "destinationKey": "=data", "options": {}}, "id": "ab9fb44a-85c6-486b-b5de-0f9d768d91b2", "name": "Extract Document Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [920, 640], "alwaysOutputData": true}, {"parameters": {"options": {"metadata": {"metadataValues": [{"name": "file_id", "value": "={{ $('Local File Trigger').item.json.path }}"}]}}}, "id": "b5abf0a5-f62a-49a6-bf81-bf002ba4bb90", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1300, 880]}, {"parameters": {"chunkSize": 100, "options": {}}, "id": "1e2791c1-86e7-4fe1-a76d-732f87e6f41a", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [1300, 1100]}, {"parameters": {"model": "nomic-embed-text:latest"}, "id": "909713ea-5be3-4916-b12e-a8e848c949cb", "name": "Embeddings Ollama1", "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [1140, 880], "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"content": "## Local RAG AI Agent with Cha<PERSON> Interface", "height": 527.*************, "width": 969.*************}, "id": "de473f2f-b806-45bc-a0e0-2ef0d2aa9b55", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0]}, {"parameters": {"content": "## Agent Tools for Local RAG", "height": 528.***********, "width": 583.*************, "color": 4}, "id": "b23ef570-f642-4503-87b9-3494ffdbf768", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [980, 0]}, {"parameters": {"content": "## Workflow to Create Local Knowledgebase", "height": 705.*************, "width": 1568.*************, "color": 5}, "id": "836cd765-dae8-460e-951d-66e19d0cce77", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 540]}, {"parameters": {"options": {}}, "id": "f8b5039c-4b00-453a-b30e-31a59f5d36ad", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [60, 120], "webhookId": "4b3b1838-d6b3-447e-9d79-d0931eddb9f8"}, {"parameters": {"options": {}}, "id": "2f64907b-42ef-4bd6-83ba-e97584271bc7", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [780, 120]}, {"parameters": {"httpMethod": "POST", "path": "invoke_n8n_agent", "responseMode": "responseNode", "options": {}}, "id": "9a30041f-0c14-41d8-a811-229b890bb1b7", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [60, 300], "webhookId": "4a839da9-b8a2-45f8-bcaf-c484f9a5912d"}, {"parameters": {"options": {}}, "id": "8311e22f-bddd-41f8-9d40-fde119126dc9", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [440, 120]}, {"parameters": {"assignments": {"assignments": [{"id": "75ebfdef-c8e2-4c3e-b716-1479d0cc2a73", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "59b7a20f-0626-4861-93e2-015d430c266e", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId}}", "type": "string"}]}, "options": {}}, "id": "4988a14f-a2ea-4c4b-8f66-0f1773e11ea0", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [260, 120]}, {"parameters": {"tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [1000, 240], "id": "9841944a-5a85-437d-a9ed-a3e7393d7a8d", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "3tjCDwujGZ7BlK7R", "name": "Supabase account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [1180, 380], "id": "24e6e703-33c2-426d-ab59-7c9cad0fded9", "name": "Embeddings Ollama2", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [1180, 640], "id": "d72c6f38-7766-4a10-9897-362539a6bcc0", "name": "Supabase Vector Store1", "credentials": {"supabaseApi": {"id": "3tjCDwujGZ7BlK7R", "name": "Supabase account"}}}, {"parameters": {"operation": "delete", "tableId": "documents", "filterType": "string", "filterString": "=metadata->>file_id=like.*{{ $json.path }}*"}, "id": "ab0e0395-9f88-438f-bc47-ea3913b869fe", "name": "Delete Old Doc Rows", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [460, 840], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "3tjCDwujGZ7BlK7R", "name": "Supabase account"}}}, {"parameters": {"triggerOn": "folder", "path": "/data/shared", "events": ["add", "change"], "options": {"followSymlinks": true, "usePolling": true}}, "type": "n8n-nodes-base.localFileTrigger", "typeVersion": 1, "position": [60, 840], "id": "eba68fe1-738d-451b-a2a7-9ee2942dd727", "name": "Local File Trigger"}, {"parameters": {"fileSelector": "={{ $('Local File Trigger').item.json.path }}", "options": {"dataPropertyName": "=data"}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [640, 640], "id": "b9459d4d-836c-47c4-9651-ebf3129f8864", "name": "Read/Write Files from Disk", "executeOnce": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e051736f-949a-4230-bf32-c9ade2674b12", "leftValue": "={{ $json.event }}", "rightValue": "add", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [280, 660], "id": "27123d6a-e27a-49a9-bd73-8a27235928ea", "name": "If"}], "pinData": {}, "connections": {"Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Ollama Model": {"ai_languageModel": [[{"node": "Vector Store Tool", "type": "ai_languageModel", "index": 0}]]}, "Extract Document Text": {"main": [[{"node": "Supabase Vector Store1", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Supabase Vector Store1", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings Ollama1": {"ai_embedding": [[{"node": "Supabase Vector Store1", "type": "ai_embedding", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Vector Store Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase Vector Store": {"ai_vectorStore": [[{"node": "Vector Store Tool", "type": "ai_vectorStore", "index": 0}]]}, "Embeddings Ollama2": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Delete Old Doc Rows": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Local File Trigger": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Extract Document Text", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}], [{"node": "Delete Old Doc Rows", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "ea9ff68c-8fc0-40b0-aa5d-48217cda89f3", "meta": {"instanceId": "73cb7a3e883df514bb47e8d1b34526d30e2abb8f56cd99f10d5948a1e11b25aa"}, "id": "hrnPh6dXgIbGVzIk", "tags": []}