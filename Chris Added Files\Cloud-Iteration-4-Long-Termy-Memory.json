{"name": "AI Agent Mastery P4", "nodes": [{"parameters": {"options": {}}, "id": "41f286ba-3481-44e1-9601-670191b1ec9a", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-1020, 560], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Set File ID').first().json.file_id }}"}, {"name": "file_title", "value": "={{ $('Set File ID').first().json.file_title }}"}, {"name": "file_url", "value": "={{ $('Set File ID').first().json.file_url }}"}]}}}, "id": "ac06c329-e3ab-4eed-a5d0-f2c069bc1558", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1660, 1600]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "dac39b92-0ec9-4077-83b8-498cf91dbea2", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [1420, 1600], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"content": "## Agent Tools for Agentic RAG", "height": 389, "width": 543, "color": 4}, "id": "********-4308-463a-8b29-6d48ddab361d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-220, 600]}, {"parameters": {"content": "## Tool to Add Google Drive Files to Vector DB", "height": 867, "width": 3373, "color": 5}, "id": "127ae507-af6d-4f6d-8c37-68efc3a7f4ef", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1280, 1000]}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Set File ID').item.json.file_id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "id": "bab9858c-4dd0-45bb-b6fe-3d84f0de7ebe", "name": "Download File", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [80, 1280], "executeOnce": true, "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9", "mode": "list", "cachedResultName": "n8n Documents", "cachedResultUrl": "https://drive.google.com/drive/folders/1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9"}, "event": "fileCreated", "options": {}}, "id": "b97bc250-ac76-4c1a-89cd-d6e2277596cc", "name": "File Created", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1220, 1120], "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9", "mode": "list", "cachedResultName": "n8n Documents", "cachedResultUrl": "https://drive.google.com/drive/folders/1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9"}, "event": "fileUpdated", "options": {}}, "id": "640efc5f-9094-4a78-9f1d-6e2bf9e87cf5", "name": "File Updated", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1220, 1280], "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"operation": "text", "options": {}}, "id": "23cd4eb5-3bc9-4229-be29-71c131836e9a", "name": "Extract Document Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [860, 1600], "alwaysOutputData": true}, {"parameters": {}, "id": "b14b0b9a-7075-44b7-b96d-2a91723355e7", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-840, 560], "notesInFlow": false, "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "delete", "tableId": "documents", "filterType": "string", "filterString": "=metadata->>file_id=like.*{{ $json.file_id }}*"}, "id": "ca70752b-6d1b-4c60-9074-818a4c7ceb15", "name": "Delete Old Doc Rows", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-600, 1120], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.id }}", "type": "string"}, {"id": "f4536df5-d0b1-4392-bf17-b8137fb31a44", "name": "file_type", "value": "={{ $json.mimeType }}", "type": "string"}, {"id": "77d782de-169d-4a46-8a8e-a3831c04d90f", "name": "file_title", "value": "={{ $json.name }}", "type": "string"}, {"id": "9bde4d7f-e4f3-4ebd-9338-dce1350f9eab", "name": "file_url", "value": "={{ $json.webViewLink }}", "type": "string"}]}, "options": {}}, "id": "544ad845-2367-4b47-a47c-34894a8b89e4", "name": "Set File ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-820, 1260]}, {"parameters": {"content": "## RAG AI Agent with Cha<PERSON> Interface", "height": 765, "width": 1036}, "id": "6dffa0d2-51ca-447b-8197-fdf46940b80d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1280, 220]}, {"parameters": {"options": {}}, "id": "589848a8-83c9-4d98-b6bc-0b002b460d0c", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-420, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId}}", "type": "string"}]}, "options": {}}, "id": "0393b19b-d183-4ed1-848f-095b4dbf7d49", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-980, 300]}, {"parameters": {"public": true, "options": {}}, "id": "a35dedf6-c50b-4a76-9cef-889da0abea18", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1240, 300], "webhookId": "6324c9bc-a935-4504-939f-2240f888c3e3"}, {"parameters": {"httpMethod": "POST", "path": "ad219ac6-d8c1-47ad-b37e-77aa07b8e8cc", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "01c9a850-75ca-4f1a-b692-4b019ca9751b", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1240, 500], "webhookId": "ad219ac6-d8c1-47ad-b37e-77aa07b8e8cc", "credentials": {"httpHeaderAuth": {"id": "6wzSkRM1jflKXEHm", "name": "<PERSON><PERSON><PERSON>"}}}, {"parameters": {"operation": "pdf", "options": {}}, "id": "e50450d1-cdb3-4ae5-a0b6-193e4876e1e8", "name": "Extract PDF Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [860, 1040]}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "f5455b7c-46be-4d2c-b945-99a63f2b1b15", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [900, 1220]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "19db6279-9855-4de3-89c6-6b39ee56ab90", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [1100, 1300]}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are a personal assistant who helps answer questions from a corpus of documents. The documents are either text based (Txt, docs, extracted PDFs, etc.) or tabular data (CSVs or Excel documents).\n\nYou are given tools to perform RAG in the 'documents' table, look up the documents available in your knowledge base in the 'document_metadata' table, extract all the text from a given document, and query the tabular files with SQL in the 'document_rows' table.\n\nBefore doing anything, use the memory tool to fetch relevant memories. You prioritize using this tool first!\n\nWhen looking up documents, start by performing RAG unless the question requires a SQL query for tabular data (fetching a sum, finding a max, something a RAG lookup would be unreliable for). If RAG doesn't help, then look at the documents that are available to you, find a few that you think would contain the answer, and then analyze those.\n\nAlways tell the user if you didn't find the answer. Don't make something up just to please them."}}, "id": "500809ba-53c9-4456-b3cf-9952dca5f1dc", "name": "RAG AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-760, 300]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/pdf", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "2ae7faa7-a936-4621-a680-60c512163034", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "fc193b06-363b-4699-a97d-e5a850138b0e", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "=application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b69f5605-0179-4b02-9a32-e34bb085f82d", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "extra"}}, "id": "d2285ab2-6522-42a4-b5e5-7421eab57513", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [320, 1260]}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "id": "4d7fa0dc-8a63-4932-9c9c-a0083f1593df", "name": "Insert into Supabase Vectorstore", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [1600, 1380], "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "4cd711dd-473a-42c0-815e-fcd499aff2ac", "name": "Extract from Excel", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [680, 1220]}, {"parameters": {"assignments": {"assignments": [{"id": "f422e2e0-381c-46ea-8f38-3f58c501d8b9", "name": "schema", "value": "={{ $('Extract from Excel').isExecuted ? $('Extract from Excel').first().json.keys().toJsonString() : $('Extract from CSV').first().json.keys().toJsonString() }}", "type": "string"}, {"id": "bb07c71e-5b60-4795-864c-cc3845b6bc46", "name": "data", "value": "={{ $json.concatenated_data }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1600, 1160], "id": "6c781024-1a52-4680-878f-9394833bab80", "name": "<PERSON>"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [680, 1400], "id": "c4055f74-f529-46e0-bb34-b59aea1c3e22", "name": "Extract from CSV"}, {"parameters": {"content": "## Run Each Node Once to Set Up Database Tables", "height": 380, "width": 1040, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-1280, -180], "typeVersion": 1, "id": "673829ed-bcc5-4a1c-bade-d01c2cd8ffbf", "name": "Sticky Note3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_metadata (\n    id TEXT PRIMARY KEY,\n    title TEXT,\n    url TEXT,\n    created_at TIMESTAMP DEFAULT NOW(),\n    schema TEXT\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-720, -60], "id": "7970298f-9c9f-4eb3-bdeb-0ce2a05cd43a", "name": "Create Document Metadata Table", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_rows (\n    id SERIAL PRIMARY KEY,\n    dataset_id TEXT REFERENCES document_metadata(id),\n    row_data JSONB  -- Store the actual row data\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-460, -60], "id": "cd042b6f-f4f5-44a4-8e8f-f0e3e401bc9c", "name": "Create Document Rows Table (for Tabular Data)", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to fetch all available documents, including the table schema if the file is a CSV or Excel file.", "operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "returnAll": true, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-180, 840], "id": "fbac20f5-047c-48ee-95e8-92a33e943bd3", "name": "List Documents", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Given a file ID, fetches the text from the document.", "operation": "execute<PERSON>uery", "query": "SELECT \n    string_agg(content, ' ') as document_text\nFROM documents\n  WHERE metadata->>'file_id' = $1\nGROUP BY metadata->>'file_id';", "options": {"queryReplacement": "={{ $fromAI('file_id') }}"}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-100, 720], "id": "ae13a65f-ab56-42d8-a763-a664defd733c", "name": "Get File Contents", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Run a SQL query - use this to query from the document_rows table once you know the file ID you are querying. dataset_id is the file_id and you are always using the row_data for filtering, which is a jsonb field that has all the keys from the file schema given in the document_metadata table.\n\nExample query:\n\nSELECT AVG((row_data->>'revenue')::numeric)\nFROM document_rows\nWHERE dataset_id = '123';\n\nExample query 2:\n\nSELECT \n    row_data->>'category' as category,\n    SUM((row_data->>'sales')::numeric) as total_sales\nFROM dataset_rows\nWHERE dataset_id = '123'\nGROUP BY row_data->>'category';", "operation": "execute<PERSON>uery", "query": "{{ $fromAI('sql_query') }}", "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [0, 840], "id": "3795d481-2a70-4326-ba10-2eb9b7e4e23c", "name": "Query Document Rows", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [160, 840], "id": "ee6bfdf4-9fbe-497b-be4a-3097b48c5882", "name": "Embeddings OpenAI2", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1000, 1120], "id": "d25a8ba0-c9b2-43b3-92bd-6d0719ec56cb", "name": "Loop Over Items"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Enable the pgvector extension to work with embedding vectors\ncreate extension vector;\n\n-- Create a table to store your documents\ncreate table documents (\n  id bigserial primary key,\n  content text, -- corresponds to Document.pageContent\n  metadata jsonb, -- corresponds to Document.metadata\n  embedding vector(1536) -- 1536 works for OpenAI embeddings, change if needed\n);\n\n-- Create a function to search for documents\ncreate function match_documents (\n  query_embedding vector(1536),\n  match_count int default null,\n  filter jsonb DEFAULT '{}'\n) returns table (\n  id bigint,\n  content text,\n  metadata jsonb,\n  similarity float\n)\nlanguage plpgsql\nas $$\n#variable_conflict use_column\nbegin\n  return query\n  select\n    id,\n    content,\n    metadata,\n    1 - (documents.embedding <=> query_embedding) as similarity\n  from documents\n  where metadata @> filter\n  order by documents.embedding <=> query_embedding\n  limit match_count;\nend;\n$$;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1220, -60], "id": "947bb2ac-1f69-4923-a4a5-af92128ccce7", "name": "Create Documents Table and Match Function", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "delete", "tableId": "document_rows", "filters": {"conditions": [{"keyName": "dataset_id", "condition": "eq", "keyValue": "={{ $('Set File ID').item.json.file_id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-380, 1280], "id": "e4e9b644-837e-4ffe-92e0-6486cd21ca78", "name": "Delete Old Data Rows", "alwaysOutputData": true, "executeOnce": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "title": "={{ $('Set File ID').item.json.file_title }}", "url": "={{ $('Set File ID').item.json.file_url }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": false}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-140, 1140], "id": "c2b279d7-c5db-44b7-8382-240bb4a37db6", "name": "Insert Document Metadata", "executeOnce": true, "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_rows", "mode": "list", "cachedResultName": "document_rows"}, "columns": {"mappingMode": "defineBelow", "value": {"dataset_id": "={{ $('Set File ID').item.json.file_id }}", "row_data": "={{ $json.toJsonString().replaceAll(/'/g, \"''\") }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "dataset_id", "displayName": "dataset_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_data", "displayName": "row_data", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [900, 1400], "id": "cd738995-1216-4206-ad07-abdbd6355c4c", "name": "Insert Table Rows", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "schema": "={{ $json.schema }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [1800, 1160], "id": "db71aee9-2623-48c0-8778-efa5e261ff1c", "name": "Update <PERSON><PERSON>a for Document Metadata", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"chunkSize": 400, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [1560, 1720], "id": "a3cb0a26-16ca-4805-a767-948dd90ecdcc", "name": "Recursive Character Text Splitter"}, {"parameters": {"content": "## Save Long Term Memories", "height": 360, "width": 1620, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [-220, 220], "typeVersion": 1, "id": "3a25877f-91b7-4e60-a59d-70f11c57c172", "name": "Sticky Note4"}, {"parameters": {"jsonSchemaExample": "{\n\t\"memories\": [\"Memory 1\", \"Memory 2\", \"Memory 3\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [60, 440], "id": "16518552-2c18-4419-9113-ab8113e94bd6", "name": "Structured Output Parser"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Create a table to store your documents\ncreate table memories (\n  id bigserial primary key,\n  content text, -- corresponds to Document.pageContent\n  metadata jsonb, -- corresponds to Document.metadata\n  embedding vector(1536) -- 1536 works for OpenAI embeddings, change if needed\n);\n\n-- Create a function to search for documents\ncreate function match_memories (\n  query_embedding vector(1536),\n  match_count int default null,\n  filter jsonb DEFAULT '{}'\n) returns table (\n  id bigint,\n  content text,\n  metadata jsonb,\n  similarity float\n)\nlanguage plpgsql\nas $$\n#variable_conflict use_column\nbegin\n  return query\n  select\n    id,\n    content,\n    metadata,\n    1 - (memories.embedding <=> query_embedding) as similarity\n  from memories\n  where metadata @> filter\n  order by memories.embedding <=> query_embedding\n  limit match_count;\nend;\n$$;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-960, -60], "id": "c71cddce-8acc-4540-ade9-80fa652fbdb8", "name": "Create Memories Table and Match Function", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [960, 820], "id": "3543f10d-2bf0-415e-8d14-56731b9304df", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $('Basic LLM Chain').item.json.output.memories }}", "options": {"metadata": {"metadataValues": [{"name": "timestamp", "value": "={{ $now }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1060, 680], "id": "8cbefd3e-8f82-4d9c-90e7-8c0aa49ee222", "name": "Default Data Loader1"}, {"parameters": {"chunkSize": 400}, "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [1160, 840], "id": "cc56f25a-ded4-492e-99ea-59ebc7a437ae", "name": "Character Text Splitter"}, {"parameters": {"assignments": {"assignments": [{"id": "636ddfda-13fd-4f21-ad30-fa21472ed9e7", "name": "output", "value": "={{ $('RAG AI Agent').item.json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1180, 420], "id": "508500c7-6f2a-47c0-ad88-f90de018c835", "name": "Edit Fields1", "executeOnce": true}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [-440, 820], "id": "af1442f9-7f04-46b9-a64b-a08634c42e7e", "name": "Embeddings OpenAI3", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "memories", "toolDescription": "Use this tool to fetch memories from previous conversations.", "tableName": {"__rl": true, "value": "memories", "mode": "list", "cachedResultName": "memories"}, "options": {"queryName": "match_memories"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [-540, 660], "id": "a8ec25d9-c42d-4f52-9453-d1e7f0795803", "name": "Retrieve Memories Tool", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "documents", "toolDescription": "Use RAG to look up information in the knowledgebase.", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [40, 700], "id": "007c7c2f-0a08-46a5-85cc-960b2a93d59d", "name": "Document RAG Tool", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"promptType": "define", "text": "=You are an expert at maintaining a memory database for a user, making sure that there aren't duplicate or contradictory memories.\n\nThe current memories being added to the database are:\n\n{{ $json.output.memories }}\n\nYour job is to:\n- Search the memory database to find any duplicate or contradictory memories. The tool will return memories from the search but you have to decide if they are duplicates or contradictory of the memories being added or not.\n- If you find any duplicate/contradictory memories, output a list of memories to delete (the content of the memory). Otherwise, if you find no memories to delete, output an empty list. You determine based on all the memories returned from the search if they are duplicates/contradictory. The memories you output have to match EXACTLY with the content received.", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [340, 280], "id": "055f166c-2e15-4808-ad52-a5e6b0722a10", "name": "Memory Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [420, 800], "id": "fe79c4dd-aabf-43b6-aaf4-ba3379efcfc0", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "memories", "mode": "list", "cachedResultName": "memories"}, "options": {"queryName": "match_memories"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [880, 280], "id": "8a8529a8-0508-480e-97af-e89da824ccc1", "name": "Supabase Vector Store", "executeOnce": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [640, 820], "id": "f5a6abeb-b892-44fb-bf3c-cee11077d505", "name": "Embeddings OpenAI4", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "cb436122-f430-4ea5-98d8-b99e8c52eeaa", "leftValue": "={{ $json.output.memories }}", "rightValue": "", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [160, 280], "id": "97b28cf1-e5c0-48e4-8d2d-09d33c7efaac", "name": "If"}, {"parameters": {"content": "## Memory Searcher", "height": 380, "width": 520, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [340, 600], "id": "1ebd94a1-c960-41f4-8371-ddd50221cdc5", "name": "Sticky Note5"}, {"parameters": {"promptType": "define", "text": "=You are an expert at extract key information from conversations to store as long term memories for RAG.\n\nThe user said:\n\n{{ $('Edit Fields').item.json.chatInput }}\n\nYour goal is to output a list of key memories to extract from the conversation.\n\nJust output a empty list if there is nothing worth saving, like if the user is just asking a basic question without providing additional context.", "hasOutputParser": true}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [-180, 280], "id": "ecb46bb9-cde0-4bca-b0f0-d5a6c117f129", "name": "Basic LLM Chain"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-120, 440], "id": "93186dad-adef-4229-8d53-1ef88aa6ecdd", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"content": "## Memory Chunking & Embedding", "height": 380, "width": 520, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [880, 600], "id": "f660f110-e73e-4b0d-9883-ff8d710a53b5", "name": "Sticky Note6"}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "memories", "toolDescription": "Use this tool to retrieve memories from the database. You specifically want to use this to find similar memories to what you are planning on adding so that you can do what it takes to remove duplicates.", "tableName": {"__rl": true, "value": "memories", "mode": "list", "cachedResultName": "memories"}, "topK": 8, "options": {"queryName": "match_memories"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [520, 680], "id": "6b290cef-065f-45ab-8ecf-454cd1e95c7c", "name": "Similar Memory Searcher", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"jsonSchemaExample": "[\"Memory 1\", \"Memory 2\", \"Memory 3\"]"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [640, 440], "id": "d5623ba2-f8a5-4325-a831-8f8821800def", "name": "Structured Output Parser1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM memories\nWHERE content IN ({{ $json.output.concat($('Basic LLM Chain').item.json.output.memories).map(item => `'${item.replace(/'/g, \"''\")}'`).join(', ') || '__IMPOSSIBLE_VALUE__' }})", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [680, 280], "id": "d1c18a19-8623-4907-9291-1da70962d55a", "name": "Postgres", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"content": "## Workflow Agent Tools", "height": 380, "width": 1100, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-220, -180], "id": "1e168e33-37e6-426d-8eee-12087ef4f695", "name": "Sticky Note7"}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}", "tool_type": "web_search"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-720, 700], "id": "48d2baaf-cb38-4544-8611-ba7e8cec68c7", "name": "Web Search Tool"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.tool_type }}", "rightValue": "image_analysis", "operator": {"type": "string", "operation": "equals"}, "id": "b22be562-3277-48fc-87be-f3a3930f7fd6"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a50a5a5a-cc87-4654-97ea-05090efdb416", "leftValue": "={{ $json.tool_type }}", "rightValue": "web_search", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "none"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [80, -60], "id": "44ef3c72-f0b7-4e31-a7f9-2a4360afa032", "name": "Determine Tool Type"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/web/search?q={{ $('Tool Start').item.json.query }} }}&summary=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [360, -60], "id": "9771c1ee-4b52-43d4-b696-00b2ae632c14", "name": "Brave Web Search", "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}, {"parameters": {"workflowInputs": {"values": [{"name": "tool_type"}, {"name": "query"}, {"name": "image_url"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-140, -60], "id": "302fe216-5301-4df0-9dcc-e3033f593b17", "name": "Tool Start"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/summarizer/search?key={{ $json.summarizer.key }}&entity_info=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "id": "b16c74a7-3a21-4123-ade4-09d95b428e87", "name": "Summarize Web Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, -60], "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}], "pinData": {"Tool Start": [{"json": {"tool_type": "image_analysis", "query": "https://drive.google.com/file/d/1vnjEMW9XKgWwmy17VjZO8ci2Mi7RO_9e/view?usp=drive_link"}}]}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Download File": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "File Created": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract Document Text": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_document", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Delete Old Doc Rows": {"main": [[{"node": "Delete Old Data Rows", "type": "main", "index": 0}]]}, "Set File ID": {"main": [[{"node": "Delete Old Doc Rows", "type": "main", "index": 0}]]}, "File Updated": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Extract PDF Text": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}], [{"node": "Extract from Excel", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}]]}, "Extract from Excel": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Set Schema": {"main": [[{"node": "Update <PERSON><PERSON>a for Document Metadata", "type": "main", "index": 0}]]}, "Extract from CSV": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Create Document Metadata Table": {"main": [[]]}, "List Documents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Get File Contents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Query Document Rows": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Document RAG Tool", "type": "ai_embedding", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Set File ID", "type": "main", "index": 0}]]}, "Insert into Supabase Vectorstore": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Create Documents Table and Match Function": {"main": [[]]}, "Delete Old Data Rows": {"main": [[{"node": "Insert Document Metadata", "type": "main", "index": 0}]]}, "Insert Document Metadata": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Update Schema for Document Metadata": {"main": [[]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Respond to Webhook": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader1": {"ai_document": [[{"node": "Supabase Vector Store", "type": "ai_document", "index": 0}]]}, "Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader1", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI3": {"ai_embedding": [[{"node": "Retrieve Memories Tool", "type": "ai_embedding", "index": 0}]]}, "Retrieve Memories Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Document RAG Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Memory Agent", "type": "ai_languageModel", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[]], "main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Memory Agent": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}, "Embeddings OpenAI4": {"ai_embedding": [[{"node": "Similar Memory Searcher", "type": "ai_embedding", "index": 0}]]}, "If": {"main": [[{"node": "Memory Agent", "type": "main", "index": 0}], [{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Similar Memory Searcher": {"ai_tool": [[{"node": "Memory Agent", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Memory Agent", "type": "ai_outputParser", "index": 0}]]}, "Postgres": {"main": [[{"node": "Supabase Vector Store", "type": "main", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Determine Tool Type": {"main": [[], [{"node": "Brave Web Search", "type": "main", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Determine Tool Type", "type": "main", "index": 0}]]}, "Brave Web Search": {"main": [[{"node": "Summarize Web Research", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "09a8049d-d53b-48f6-bfda-f0497e3be558", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "E4XqJtKrz29GD3Ml", "tags": []}