So at this point, we have a nice and simple

00:02
working implementation

00:03
of RAG, but there are 2 very big limitations to

00:06
RAG that I'm going to address with you now and

00:09
we'll implement solutions for in our prototype

00:12
using something called a Gentic RAG. I'll explain more in

00:15
a bit what that means. But first, what are these

00:17
limitations?

00:18
The first 1 is that our current implementation

00:21
with RAG is going to perform very poorly

00:24
on tabular data. Any kind of structured data that you

00:27
have in something like a spreadsheet like we have right

00:29
here. And the reason for that, first of all, is

00:32
that LLMs are bad at math. And so if you

00:34
were to ask it to compute the sum or the

00:37
average over a column, for example, you're not going to

00:40
get the right answer 100 percent of the time like

00:43
you would if you used a calculator because LLMs just

00:45
don't work in that same way. Just because it's a

00:48
machine doesn't mean that it's always gonna be good at

00:50
math. And then the other big limitation of reg is,

00:53
as we know, we split all of our documents into

00:55
chunks. And so imagine with me here that this spreadsheet

00:58
was actually 2000 records. We can't just send the entire

01:02
spreadsheet into the LLM, especially if we have a lot

01:05
of different columns. That's just gonna make the prompt way

01:07
too big and overwhelm it. And so when we use

01:10
RAG to split this spreadsheet into different chunks, that means

01:14
that each chunk is only going to have some of

01:16
the records. And so when we make our RAG query

01:19
and we retrieve a certain number of chunks like 4

01:23
of the top most relevant content,

01:25
we might only be pulling part of the table. And

01:28
so it doesn't even have the necessary information to make

01:31
that full calculation

01:33
for something like a maximum, a minimum, a sum, or

01:36
an average for any given column. And so we need

01:38
a solution for this because watch, I'll even show you

01:41
how this falls apart in real time. I'll ask it

01:43
to give me the average new customers per month. And

01:46
so it'll do that rag lookup, and we'll see that

01:49
it will have pretty intelligent reasoning. It might even come

01:51
close to the right answer, but it's still not going

01:54
to be right. So take a look at this. It

01:55
pulls the data and then it says that the average

01:58
of new customers is approximately 98.

02:01
So it's a decent answer and it says approximately, so

02:04
it's acknowledging that it doesn't have the perfect answer. But

02:07
yeah, I did not get the correct answer here. 98

02:10
is close

02:11
but it's not right. And I can even show you

02:13
if I go into the spreadsheet here and I just

02:15
take the average

02:17
of this value these values we get 101.25.

02:20
So I'll delete this column so any upcoming requests can't

02:24
leverage this. But, yeah, 101.25

02:26
is the correct answer. And I'll even show you in

02:29
the GENTIC RAG implementation that we are about to build

02:33
together,

02:34
we will get the right answer. So I'll paste in

02:36
the exact same question, what is the average new customers

02:38
per month? And 1 of these new tools that we're

02:41
giving our agent

02:42
is the ability to write SQL queries. And so what

02:45
we do is we take our table that we have

02:48
in a spreadsheet or a CSV file, and we turn

02:51
it into 1 of the tables that we have in

02:54
our Supabase database. And I'll show you how that works

02:56
in this video. But then we're able to write SQL

02:59
queries so that we can calculate

03:01
something like the average for new customers

03:03
without having to look at every single row in the

03:07
table. We don't want our LLM to have to do

03:08
that. And sure enough, 101.25,

03:11
it gets the exact right answer this time. And we're

03:14
gonna be able to repeat this over and over and

03:16
over again as if we had a calculator because that's

03:18
essentially what we have when we can write these SQL

03:21
queries. And so, yeah, this is working super, super nice.

03:24
So I hope this limitation of working with structured data

03:27
is very clear to you. Now the second huge limitation

03:30
of Rang that I've already mentioned now is that we

03:34
sometimes don't want to split our documents into chunks when

03:38
we are feeding them into our LLM.

03:40
Because RAG is very powerful for directed lookups. When you

03:44
want to extract just a bit of information from a

03:47
document like when we looked up the overall satisfaction score

03:51
for example.

03:52
But other times, and this is possible only when your

03:54
documents are small enough, but this happens a lot where

03:57
you want to give the entire

03:59
document

04:00
to the LLM and not just a single chunk or

04:03
a couple of chunks from it. And the reason for

04:05
that is sometimes 1 chunk is only going to make

04:08
sense within the context of other chunks that came before

04:12
it or after it. And so again, if your documents

04:15
are small enough, we are not gonna overwhelm the l

04:17
m by sending in the whole thing. Sometimes you want

04:20
to do that, and so you can have a more

04:22
holistic view of the data that you're looking at. And

04:25
so we have implemented a solution for that as well

04:27
in this prototype. This is the second piece and I'll

04:30
show you how to build this all in a bit

04:32
as well. So we have a couple of new tools

04:35
here

04:36
to list the documents that are available in our knowledge

04:39
base. And so instead of just doing a directed lookup,

04:41
we can get a higher level view, what documents are

04:44
available to us. And then we have another tool that

04:47
can get the entire contents of a file. So now

04:50
let's see this in action. I'm going to explicitly

04:53
ask it not to use rag, so it'll look up

04:55
the full document for the meeting minutes. I know it's

04:58
a little silly that I'm asking it not to use

05:00
a tool here. It's just difficult in a test environment

05:03
with a kind of sample prompt

05:05
to get it to use this reliably.

05:07
If you have a lot of documents and you're very

05:10
clear in your system prompt when and how to use

05:13
these different tools, like use RAG in this case or

05:15
look up your documents and view the entire file in

05:19
this case, then you can get it to use these

05:20
different tools depending on which 1 is going to get

05:23
the job done better. But since we're in a prototype

05:25
here, I don't have that fully configured at this point.

05:27
So I'm just going to ask it to look up

05:29
the file explicitly. And so now, it'll list the documents

05:32
to get that path to the file and then fetch

05:35
the full contents like we can see in the response

05:38
right here. And there we go. We got the right

05:39
answer. We got our attendees

05:41
for our meeting. And again, RAG would work with this,

05:45
but I just wanted to show you an example here.

05:47
And you can definitely envision certain documents where it'd be

05:51
a few pages long, a couple of chunks won't cut

05:53
it, but it's also small enough that you could fit

05:55
into the prompt for the LLM. In that kind of

05:57
case, you would definitely want to pull the entire document

06:00
into the prompt for the LLM,

06:02
especially if you have a response that requires

06:05
analyzing different parts of the file that would be split

06:08
between different chunks. So that's everything we're adding right here

06:11
for

06:12
agentic reg.

06:13
And agentic reg itself is a topic

06:16
that covers a lot of different kinds of functionality built

06:20
on top of reg, not just these 2 that I

06:21
have here. So let me cover with you quick what

06:24
that is, then we'll dive into our implementation of agentic

06:27
reg and how I changed everything in the reg pipeline,

06:31
and we'll dive into these tools more as well. So

06:33
the very straightforward

06:35
definition

06:36
of agentic reg is it's giving your AI agent the

06:39
ability to explore the knowledge base in different ways. And

06:43
so the diagram that we're looking at right here is

06:45
an implementation of what's called naive reg. This is more

06:48
like our first implementation of reg in the last video,

06:51
where we chunk up our documents,

06:53
we use the embedding model to put them in a

06:55
vector database, and then whenever the user query comes in,

06:59
we just find the relevant context to then include in

07:01
the prompt to the LLM. In this case, the LLM

07:05
isn't very

07:06
agentic when it comes to reg because it's not able

07:09
to decide

07:10
how it wants to explore the knowledge base. We're just

07:13
always force feeding it the most relevant context based on

07:16
the user question. That's it. It doesn't really make much

07:19
of a decision here. And our first implementation is a

07:22
little bit different because it gets to decide the query

07:25
for rank. In this case, in this diagram, we just

07:28
happen to be throwing in the full user question to

07:31
match with the vector database. So our old implementation is

07:35
a little bit agentic,

07:36
but still with what we just saw with it being

07:39
able to write SQL queries, to look at our tables

07:42
better, to be able to explore entire documents and not

07:45
just look up chunks. It has all these different options

07:48
available to it and in our system prompt to the

07:50
LLM, we can tell it when and how to use

07:53
these different approaches based on what works best for our

07:55
use case. That's what makes it agentic. It can make

07:58
a decision. It's now non deterministic

08:01
how it leverages

08:02
our knowledge base.

08:04
And so there's another diagram here also by Weeviate by

08:07
the way. This is from a Weeviate article. There's another

08:10
diagram that shows kind of what this looks like in

08:12
a more general sense. And so like I said earlier,

08:15
a gentry greg is not a 1 size fits all

08:18
approach. You can just think of a million different tools

08:21
that you could give to your LLM to explore the

08:23
knowledge base in different ways based on the documents that

08:26
you have, the metadata,

08:27
writing SQL queries like we're doing, even looking at multiple

08:30
vector databases. This is another example of agentic reg where

08:34
the LLM actually gets to decide which collection of information

08:37
it's looking at based on the user question. And so

08:40
I'm just really driving home this point to you here

08:43
that a gente Greg is just this really powerful higher

08:46
level idea of let the LLM decide how to look

08:50
at your documents. Give it different tools to do things

08:53
depending on the question because a lot of times

08:56
the question that comes in is going to dictate a

08:59
very different

09:00
answer compared to the last question. You have to leverage

09:03
your data differently for that. And then agentic reg also

09:06
means including other tools like a calculator or web search

09:09
to augment the response that it gets based on the

09:12
context from the vector database. So that's agentic reg as

09:16
a whole. Now let's build out our solution. So first

09:19
things first, for our implementation of agentic reg, there are

09:22
a couple of new tables that we have to create

09:25
in our Supabase database. And so we have this first

09:28
node that we already covered that creates our documents table

09:32
that we've already been working with for reg, but now

09:35
we have these 2 as well. So you can go

09:37
ahead and run these just like you ran the other

09:39
1. This is going to create 2 tables. First, we

09:42
have our document metadata.

09:44
This is where we store the higher level information for

09:48
our documents so we know what docs are available to

09:51
us to pull the contents from. And then we also

09:54
have the document rows table. This is where we store

09:58
all of our tabular data so we can write sequel

10:01
queries

10:02
to query

10:03
all of this information instead of having to look up

10:06
the file directly. That's where we saw all those performance

10:09
gains where RAG would fail horribly on a table, but

10:12
we can write queries on this data to make those

10:15
deterministic calculations in a much more accurate way. And So

10:18
let me cover the details of these really quick then

10:20
we'll get into building these out in the workflow.

10:23
So for the metadata, we're storing things like the ID

10:26
of the file in Google Drive and for the local

10:28
AI implementation that's just gonna be the file path. Then

10:31
we have the title, when it was created, and the

10:34
URL for the file in Google Drive as well. And

10:36
then the other key piece of information that we wanna

10:38
store but only for our spreadsheet files is the schema.

10:43
We want to tell the AI agent

10:45
the columns that are available for it to query to

10:48
find things like the maximum

10:50
or the sum of a column for example. And so

10:53
this value is null just for the files that aren't

10:56
tables. But then for example for our revenue metrics, we

11:00
have month, new customers, total customer. We need to give

11:02
all these to the L. L. M. And so that's

11:04
how we do that right here. So you can see

11:07
all of these same columns listed out right here. And

11:10
then for the document rows, this is super neat.

11:13
Postgres

11:14
or Supabase

11:15
has this field type called JSONB. So essentially, we can

11:19
represent

11:20
a bunch of data sort of as a sub table

11:23
almost where we have all of the keys for the

11:26
different columns that map to that value for this specific

11:29
record. So this is how even though all of our

11:32
different tables are going to have different schemas, like let's

11:35
look at the cohort analysis, we have entirely different columns

11:38
with different data compared to the revenue metrics. We're able

11:41
to store all these records in a single table. We

11:44
don't have to create multiple SQL tables because we're leveraging

11:48
JSON b, which is a powerful data type available to

11:51
us in our Postgres databases. And so this is 1

11:54
of the more advanced parts of our agentic RAG implementation.

11:58
But I hope that makes sense how for 1 table

12:01
we store these pieces of information, and then for another

12:03
table we store these pieces of information, and we can

12:07
have this be whatever keys and values we want because

12:09
it's just stored as JSON in the database. And so

12:12
then when we write our queries we just query within

12:15
the row data

12:17
column that we have within document rows. And then the

12:20
data set ID, this corresponds to the specific file that

12:23
this is a row for. Like the data set ID

12:26
for a row within here would be this value that

12:29
we have for the ID of this spreadsheet.

12:32
So that's the database setup as a whole. Now we

12:35
can dive into how we configured all of this. And

12:38
so going into our reg pipeline,

12:40
things look pretty similar. There just are a couple of

12:43
additions that we have to have to populate not just

12:46
the documents table but our other 2 new tables as

12:49
well.

12:50
And so the first thing that we do is after

12:52
we get a blank slate, we clear all records from

12:56
the documents table and now the document rows table as

12:59
well. Now we insert our metadata. So we just add

13:02
a record that's going to give us the initial information

13:06
for the file like the ID and the title. So

13:08
we set all that right here in this node and

13:11
then the rest of the workflow we download the file,

13:13
we have the same switch statement where we extract the

13:16
text based on the file type.

13:18
1 thing though for the tabular files like excel and

13:21
c s v files, we also need to insert the

13:24
table rows at this point. And so this step is

13:27
going to run for each of the individual rows that

13:31
we extracted from the file and so we'll insert these

13:34
rows 1 at a time. So we set that data

13:37
set ID so that we know

13:39
which file this row is for. And then the other

13:42
piece of information that we set is obviously the row

13:44
data itself. And there's just a little bit of JavaScript

13:47
code here to clean up this string, make it so

13:49
that we can store it in this format as JSON.

13:52
And so this runs for every single record, so now

13:55
we have the document rows table populated for this file

13:58
as well. And then the very last thing that we

14:00
have to do

14:01
is we have to set the schema for the tabular

14:05
files. And so going back to the document metadata,

14:09
when we first insert a record for a spreadsheet file,

14:13
We don't have the schema yet because we haven't actually

14:16
extracted the content from it. So way back here at

14:19
the start when we insert the document metadata, we don't

14:21
actually know what the schema is yet. So we initially

14:24
set the ID and the title, those higher level pieces

14:27
of information,

14:28
But then at the very end once we've extracted

14:31
the schema for the file, like we have these columns

14:35
now,

14:36
now we can go back to the metadata and update

14:38
the schema here. And so we insert at the beginning

14:41
and then we set the schema at the end. I

14:43
hope that makes sense how we need to do that

14:44
later because we have to do it after we have

14:47
extracted the text from the file. So that's all we

14:49
do right here. We just run another update here to

14:51
update this record based on the file ID just setting

14:54
the schema. And we only do this if it is

14:57
a spreadsheet file otherwise we just have the schema left

15:00
as null. And so that's our rank pipeline. It didn't

15:03
take that much to add that it's all about just

15:05
getting the data in the right format for our AI

15:08
agent and we have that now. So now we can

15:10
move on to these extra tools that we have to

15:13
explore our knowledge base in different ways. That's what truly

15:15
makes it agentic rag. And luckily for our agentic rag

15:18
tools, they're pretty simple. And so let's go over each

15:21
of them really quick. So first, we have our tool

15:23
to list all of the documents that are available to

15:26
us in our document metadata table. So just a very

15:29
simple select operation. And we are returning all of the

15:33
documents so that our agent has access to all of

15:36
those titles so it can pick the 1 it wants

15:38
to look at. If your knowledge base is massive and

15:41
you have thousands or hundreds of thousands of documents, you're

15:45
definitely gonna wanna change this so you don't overwhelm the

15:48
LLM

15:48
with hundreds of thousands of document titles that you pull

15:52
from this database. And so keep that in mind, This

15:54
is working for me just because I only have around

15:57
a dozen records here, and it will work even if

16:00
you have a few hundred. It doesn't add that much

16:02
to the context length. But, yeah, if you have many,

16:05
many, many documents, you wanna add some sort of custom

16:08
filtering, you could even implement rag on top of the

16:11
document metadata table. That's outside of the scope of this

16:15
course here, but just know that there are a lot

16:17
of different strategies that you can work with. I just

16:19
wanna get you started here especially because it is a

16:22
prototype. And then for the description of this tool, we're

16:25
telling the LLM, remember this goes into the prompt for

16:27
the LLM, we're telling it to use this tool to

16:29
fetch all available documents

16:31
and the important thing added on here is that this

16:34
is also how you get the table schema for any

16:36
CSV or Excel file so that knows that this is

16:39
available to it. We're telling it here in the tool

16:42
description. And then the second tool is the 1 to

16:44
get the contents of a specific file. And so once

16:48
it knows the ID, so it's gonna have to call

16:50
the list tool to fetch that ID. Once it knows

16:53
that, then it can call this tool. So that's the

16:56
parameter

16:56
that's decided by the LLM, the file ID. And then

17:00
we have this SQL query that's run

17:03
to get the contents of that file. And so

17:06
based on the file ID, which is the parameter here,

17:10
it's going to go to the documents table and it's

17:13
going to collect all of the chunks for that specific

17:16
file and combine it all together to give you that

17:19
original full file. And the reason that I'm doing it

17:21
this way, I could store all of the document

17:25
content in the metadata table as well, but that would

17:28
just kind of be a duplicate. Like it's already here

17:30
in the documents table, this is how n 8 n

17:32
has their rag setup. So I can't really control that

17:35
so I might as well just leverage all the information

17:37
being stored in the chunks in this content column. So

17:40
I'm just aggregating them all together for a single file

17:43
and then I return that to the LLM.

17:46
Yeah. This 1 is a very very simple description.

17:48
Given a file ID, fetches the text from the document.

17:51
Nice and easy. And you could always change these descriptions

17:54
or update your system prompt if you're not happy with

17:57
how the LLM is deciding to use these different tools.

18:00
And so that's a big part of the experimentation with

18:03
agentic reg. You want to come up with these tools

18:05
because again this is just a starting point for you.

18:08
You could implement many other tools for agentic reg and

18:12
the way that you instruct the LLM on how to

18:14
use these tools is all going to depend on your

18:17
specific use case. And so play around with these prompts,

18:20
especially the system prompt.

18:22
It's really worth spending that time to optimize the instructions

18:26
that you're giving to your agent. And then the very

18:28
last tool that we have is the 1 to query

18:30
the document rows, to write the SQL queries, to go

18:34
to document rows and based on this JSONB,

18:37
it can write queries to get the things like the

18:39
average over initial customers, for example, in the cohort analysis

18:44
file. And the instructions for this tool are a little

18:47
bit more in-depth because I want to be very clear

18:50
to it including

18:52
with examples

18:53
on how to write these SQL queries. So I describe

18:57
to it like, hey, you're working with this document rows

18:59
table.

19:00
You have all the rows stored as JSON in this

19:02
JSON b column, and then I give it some examples

19:05
of how it would write queries

19:07
to leverage this successfully.

19:09
And so the parameter that it decides is the entire

19:12
SQL query. So it'll generate the full query based on

19:15
the instructions and examples that I have right here like

19:18
we saw earlier in the demo. And so now we

19:20
can just test it out again. I'll go back to

19:21
this, spreadsheet we're just looking at for cohort analysis.

19:25
I can, let's say, maybe calculate the sum of the

19:28
initial customers and so I'll go back here, open up

19:31
a chat and I'll say what is the sum of

19:34
the

19:34
initial

19:35
customers

19:36
for the cohort.

19:39
Alright.

19:39
And so in this case, again, the LLM should not

19:42
use rag for this and it decides it doesn't want

19:44
to. So there we go. So it lists the documents

19:46
so it knows the spreadsheet to look up and it

19:48
has the schema and then we query the document rows.

19:51
The sum of the initial customers is 4282.

19:55
And let's just do a quick sum here. So I'll

19:56
do sum and, yep, 4282.

20:00
Alright. We got the right answer. And then we can

20:01
even go into our query document rows execution and see

20:04
the exact query that it generated

20:06
to get that sum for the initial customer column. It

20:10
is just beautiful and it generates these queries successfully

20:14
very, very consistently, which is impressive especially because this entire

20:18
time we're only using GPT 4 0 mini for our

20:20
model. There are dozens of LLMs that are much more

20:23
powerful.

20:24
So that is the power of agentic reg. And then

20:27
for our local AI implementation,

20:29
there is literally nothing different. So we have these 2

20:32
nodes that you want to run to create those different

20:35
tables for document metadata and then also the document rows.

20:38
There's not that third 1. If you noticed in the

20:40
last video, it turns out that the, Postgres p g

20:44
vector store node creates the document table for you unlike

20:48
Supabase. And so we have to explicitly create documents

20:52
ourselves for Supabase for the cloud implementation.

20:55
But for Postgres,

20:57
it makes it for us. And so that's why we

20:59
only have 2 here. But, yeah, you wanna create those

21:00
tables. And then the rest of our workflow,

21:03
including our reg pipeline and the tools that we have

21:06
for agentic reg, These are all exactly the same. And

21:10
so that is a agentic reg as a whole.

21:13
Super powerful implementation and it's only gonna keep getting better

21:16
when we go on to the code version

21:19
of our agent that we're building into this course. And

21:22
so with that, we can move on to the next

21:24
part of our prototype, another really important component which is

21:27
agent memory.