{"name": "AI Agent Mastery Local P3", "nodes": [{"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Set File ID').first().json.file_id }}"}, {"name": "file_title", "value": "={{ $('Set File ID').first().json.file_title }}"}]}}}, "id": "ad5e0545-10dc-4e49-8746-cd98d79e77e9", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1000, 1140]}, {"parameters": {"content": "## Agent Tools for Agentic RAG", "height": 529, "width": 583, "color": 4}, "id": "4c52db51-76c8-4f74-b0b2-9fd2e8006b73", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-680, 0]}, {"parameters": {"content": "## Tool to Add a Google Drive File to Vector DB", "height": 867, "width": 3073, "color": 5}, "id": "40969f8c-a0e8-4c42-b8cf-aa60828b9cc8", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1740, 540]}, {"parameters": {"operation": "text", "options": {}}, "id": "3521d7ad-9107-485e-b525-f936896fe074", "name": "Extract Document Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [200, 1140], "alwaysOutputData": true}, {"parameters": {}, "id": "e7a613b7-6180-458d-8b8d-76be9fe5cab2", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-1320, 360], "notesInFlow": false, "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.path }}", "type": "string"}, {"id": "f4536df5-d0b1-4392-bf17-b8137fb31a44", "name": "file_type", "value": "={{ $json.path.split(/[\\\\/]/).pop().split('.').pop(); }}", "type": "string"}, {"id": "77d782de-169d-4a46-8a8e-a3831c04d90f", "name": "file_title", "value": "={{ $json.path.split(/[\\\\/]/).pop().split('.').slice(0, -1).join('.'); }}", "type": "string"}]}, "options": {}}, "id": "5db08cd2-7bb5-4895-9353-a4c748552e71", "name": "Set File ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1320, 820]}, {"parameters": {"content": "## RAG AI Agent with Cha<PERSON> Interface", "height": 525, "width": 1036}, "id": "6d82c439-eee6-47e0-8cc9-34db5ac0aca1", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1740, 0]}, {"parameters": {"options": {}}, "id": "c10d1fea-c422-420b-8258-fd342e805383", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-880, 100]}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId}}", "type": "string"}]}, "options": {}}, "id": "c530625d-eb88-4124-aea7-1d2c20f13cdd", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1440, 100]}, {"parameters": {"public": true, "options": {}}, "id": "b6f21a99-f931-4e6d-9d53-e912f9ffac96", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1700, 100], "webhookId": "5cd62465-6621-4180-a213-6594b89f0849"}, {"parameters": {"httpMethod": "POST", "path": "914b0f7f-80c2-49f2-a3c0-dca092208a4f", "responseMode": "responseNode", "options": {}}, "id": "788b2ddd-20f2-450c-a5a5-68cb13aa38d2", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1700, 300], "webhookId": "914b0f7f-80c2-49f2-a3c0-dca092208a4f"}, {"parameters": {"operation": "pdf", "options": {}}, "id": "3d6001da-d45b-44f8-9538-00ec8b7f55a5", "name": "Extract PDF Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [200, 580]}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "42856c31-2277-4c3c-8e07-9d26d3ea994f", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [240, 760]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "9ef9c687-0cdc-4bbe-bc59-6c6e376513aa", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [440, 840]}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are a personal assistant who helps answer questions from a corpus of documents. The documents are either text based (Txt, docs, extracted PDFs, etc.) or tabular data (CSVs or Excel documents).\n\nYou are given tools to perform RAG in the 'documents' table, look up the documents available in your knowledge base in the 'document_metadata' table, extract all the text from a given document, and query the tabular files with SQL in the 'document_rows' table.\n\nAlways start by performing RAG unless the users asks you to check a document or the question requires a SQL query for tabular data (fetching a sum, finding a max, something a RAG lookup would be unreliable for). If RAG doesn't help, then look at the documents that are available to you, find a few that you think would contain the answer, and then analyze those.\n\nAlways tell the user if you didn't find the answer. Don't make something up just to please them."}}, "id": "810eb22b-2ca0-4a04-a1d9-84e7ed996ee8", "name": "RAG AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-1220, 100]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "pdf", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "2ae7faa7-a936-4621-a680-60c512163034", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "xlsx", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "fc193b06-363b-4699-a97d-e5a850138b0e", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "=csv", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b69f5605-0179-4b02-9a32-e34bb085f82d", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "txt", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "extra"}}, "id": "27fffd11-d138-41f1-a403-d94a936e67ee", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-460, 800]}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "0f99f557-4c28-47bd-af8e-bd4eec6024a3", "name": "Extract from Excel", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [20, 760]}, {"parameters": {"assignments": {"assignments": [{"id": "f422e2e0-381c-46ea-8f38-3f58c501d8b9", "name": "schema", "value": "={{ $('Extract from Excel').isExecuted ? $('Extract from Excel').first().json.keys().toJsonString() : $('Extract from CSV').first().json.keys().toJsonString() }}", "type": "string"}, {"id": "bb07c71e-5b60-4795-864c-cc3845b6bc46", "name": "data", "value": "={{ $json.concatenated_data }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [880, 700], "id": "7bebee36-0def-4068-82ed-4f7af3b58f2f", "name": "<PERSON>"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [20, 940], "id": "55bfa00b-7aae-4b95-b463-89449c240032", "name": "Extract from CSV"}, {"parameters": {"content": "## Run Each Node Once to Set Up Database Tables", "height": 380, "width": 1040, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-1740, -400], "typeVersion": 1, "id": "c662e17a-279f-4786-bd40-cbf7533aee65", "name": "Sticky Note3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_metadata (\n    id TEXT PRIMARY KEY,\n    title TEXT,\n    created_at TIMESTAMP DEFAULT NOW(),\n    schema TEXT\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1540, -260], "id": "f9adfd0e-5e27-4299-8d0e-8969b6039940", "name": "Create Document Metadata Table", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_rows (\n    id SERIAL PRIMARY KEY,\n    dataset_id TEXT REFERENCES document_metadata(id),\n    row_data JSONB  -- Store the actual row data\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1100, -260], "id": "a771c555-300e-4abb-8aac-79385e3a5c3e", "name": "Create Document Rows Table (for Tabular Data)", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to fetch all available documents, including the table schema if the file is a CSV or Excel file.", "operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "returnAll": true, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-600, 360], "id": "fc230fcb-e97b-491c-acb0-6961ff8a7397", "name": "List Documents", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Given a file ID, fetches the text from the document.", "operation": "execute<PERSON>uery", "query": "SELECT \n    string_agg(text, ' ') as document_text\nFROM documents_pg\n  WHERE metadata->>'file_id' = $1\nGROUP BY metadata->>'file_id';", "options": {"queryReplacement": "={{ $fromAI('file_id') }}"}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-520, 160], "id": "e6a12fbc-bb24-48f7-995e-9e86b419e093", "name": "Get File Contents", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Run a SQL query - use this to query from the document_rows table once you know the file ID (which is the file path) you are querying. dataset_id is the file_id (file path) and you are always using the row_data for filtering, which is a jsonb field that has all the keys from the file schema given in the document_metadata table.\n\nExample query:\n\nSELECT AVG((row_data->>'revenue')::numeric)\nFROM document_rows\nWHERE dataset_id = '/data/shared/document.csv';\n\nExample query 2:\n\nSELECT \n    row_data->>'category' as category,\n    SUM((row_data->>'sales')::numeric) as total_sales\nFROM dataset_rows\nWHERE dataset_id = '/data/shared/document2.csv'\nGROUP BY row_data->>'category';", "operation": "execute<PERSON>uery", "query": "{{ $fromAI('sql_query') }}", "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-440, 340], "id": "bdaea697-12d0-439b-b62c-f111faf56356", "name": "Query Document Rows", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1500, 660], "id": "d8c0e5cc-873d-4152-865b-dcbcf61dc21e", "name": "Loop Over Items"}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "title": "={{ $('Set File ID').item.json.file_title }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-820, 680], "id": "78ffcddf-1ef3-4885-af5c-0808b7a39ec2", "name": "Insert Document Metadata", "executeOnce": true, "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_rows", "mode": "list", "cachedResultName": "document_rows"}, "columns": {"mappingMode": "defineBelow", "value": {"dataset_id": "={{ $('Set File ID').item.json.file_id }}", "row_data": "={{ $json.toJsonString().replaceAll(/'/g, \"''\") }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "dataset_id", "displayName": "dataset_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_data", "displayName": "row_data", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [240, 940], "id": "4d24131f-8daa-4ceb-b48f-dc2155ec5896", "name": "Insert Table Rows", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "schema": "={{ $json.schema }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [1100, 700], "id": "82cf38ec-7f8f-42f9-9337-8d479cc8b097", "name": "Update <PERSON><PERSON>a for Document Metadata", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"triggerOn": "folder", "path": "/data/shared", "events": ["add", "change"], "options": {"followSymlinks": true, "usePolling": true}}, "type": "n8n-nodes-base.localFileTrigger", "typeVersion": 1, "position": [-1700, 660], "id": "96bca299-dd10-4afe-b04c-d9372795d07b", "name": "Local File Trigger"}, {"parameters": {"fileSelector": "={{ $('Set File ID').item.json.file_id }}", "options": {"dataPropertyName": "=data"}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-660, 820], "id": "d4daf11a-92e4-4135-bb33-370079be2bbd", "name": "Read/Write Files from Disk"}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [760, 1140], "id": "17203ac3-4a42-4f62-929b-918348cf8163", "name": "Embeddings <PERSON><PERSON><PERSON>", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [-280, 280], "id": "24a5ab25-1f4b-471b-b670-48edc2489b2c", "name": "Embeddings Ollama1", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"chunkSize": 400, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [900, 1260], "id": "0e5e69e6-c19f-435e-b0be-712861cd8dda", "name": "Recursive Character Text Splitter"}, {"parameters": {"model": {"__rl": true, "value": "qwen2.5:14b-8k", "mode": "list", "cachedResultName": "qwen2.5:14b-8k"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1480, 360], "id": "********-8467-4a0d-9f70-fe57d5ed53c0", "name": "Ollama (Change Base URL)", "credentials": {"openAiApi": {"id": "yjDbkWOXNXC959ei", "name": "OpenAi account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DO $$\nBEGIN\n    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'documents_pg') THEN\n        EXECUTE 'DELETE FROM documents_pg WHERE metadata->>''file_id'' LIKE ''%' || $1 || '%''';\n    END IF;\nEND\n$$;", "options": {"queryReplacement": "={{ $json.file_id }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1140, 680], "id": "06d3ce63-f2b0-4e81-a549-ddd6bc00e694", "name": "Delete Old Doc Records", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM document_rows\nWHERE dataset_id LIKE '%' || $1 || '%';", "options": {"queryReplacement": "={{ $('Set File ID').item.json.file_id }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-980, 820], "id": "c7d1e053-b41d-4795-aca3-97ced928005b", "name": "Delete Old Data Records", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"mode": "insert", "tableName": "documents_pg", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [880, 920], "id": "5269b34f-a105-474c-9bf2-e0394065e839", "name": "Postgres PGVector Store", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "documents", "toolDescription": "Use RAG to look up information in the knowledgebase.", "tableName": "documents_pg", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [-380, 100], "id": "d85cc9a5-297c-458a-be28-e3434a808d78", "name": "Document RAG Tool", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"content": "## Workflow Agent Tools", "height": 380, "width": 1680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-80, 140], "id": "2105a13a-3641-4f54-8afa-72ad237b5031", "name": "Sticky Note7"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.tool_type }}", "rightValue": "image_analysis", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a50a5a5a-cc87-4654-97ea-05090efdb416", "leftValue": "={{ $json.tool_type }}", "rightValue": "web_search", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "none"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [200, 280], "id": "9bf2bfed-e223-4035-8dba-8f9f91e2e753", "name": "Determine Tool Type"}, {"parameters": {"workflowInputs": {"values": [{"name": "tool_type"}, {"name": "query"}, {"name": "image_path"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-20, 280], "id": "02679c02-dac2-4176-9429-dcf00f970897", "name": "Tool Start"}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.\n\nThis tool will return the contents of the 3 most relevant web pages from the search.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ $fromAI('query') }}", "tool_type": "web_search"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "image_path", "displayName": "image_path", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-1160, 360], "id": "ccd42b47-cce4-4de0-be22-5e94f0a7d797", "name": "Web Search Tool"}, {"parameters": {"url": "=http://searxng:8080/search?q={{ $json.query }}&format=json", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 280], "id": "185a265d-f7ab-46a8-8cf8-22f2b2f9d7ed", "name": "SearXNG"}, {"parameters": {"fieldToSplitOut": "results", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [600, 280], "id": "62ab6409-d512-42a1-9fbf-959ab92caab7", "name": "Split Out"}, {"parameters": {"assignments": {"assignments": [{"id": "169ce734-0077-4c34-b7f1-40a35184fad6", "name": "url", "value": "={{ $json.url }}", "type": "string"}, {"id": "310e45f1-904e-4350-971f-a8519a49ab91", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "f6ac5cd2-4504-4f37-a766-33bc6ef09d47", "name": "content", "value": "={{ $json.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [760, 280], "id": "8622fe64-f928-47eb-a796-578dc15f0018", "name": "Edit Fields2"}, {"parameters": {"maxItems": 3}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [920, 280], "id": "a8edd148-b29c-4dad-bde4-ef037bd661b1", "name": "Limit"}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "search_results", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1380, 280], "id": "94946fb6-fadc-4470-b0e2-be417910cd1e", "name": "Aggregate1"}, {"parameters": {"url": "={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1080, 280], "id": "1dfda00f-1225-4063-b809-5206b0f3caee", "name": "HTTP Request", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "site_html", "cssSelector": "body"}]}, "options": {}}, "type": "n8n-nodes-base.html", "typeVersion": 1.2, "position": [1220, 280], "id": "03a7e74e-618c-4371-8ac5-a0af45a92cc5", "name": "HTML", "alwaysOutputData": true, "onError": "continueRegularOutput"}], "pinData": {"Tool Start": [{"json": {"tool_type": "web_search", "query": "Best AI Agent Frameworks", "tool_type 2": "image_analysis", "query 2": "Describe this image", "image_path": "/data/shared/ArchonMCPThumbnail.jpg"}}]}, "connections": {"Extract Document Text": {"main": [[{"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Postgres PGVector Store", "type": "ai_document", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Set File ID": {"main": [[{"node": "Delete Old Doc Records", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Extract PDF Text": {"main": [[{"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}], [{"node": "Extract from Excel", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}]]}, "Extract from Excel": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Set Schema": {"main": [[{"node": "Update <PERSON><PERSON>a for Document Metadata", "type": "main", "index": 0}]]}, "Extract from CSV": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Create Document Metadata Table": {"main": [[]]}, "List Documents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Get File Contents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Query Document Rows": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Set File ID", "type": "main", "index": 0}]]}, "Insert Document Metadata": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Local File Trigger": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Embeddings Ollama": {"ai_embedding": [[{"node": "Postgres PGVector Store", "type": "ai_embedding", "index": 0}]]}, "Embeddings Ollama1": {"ai_embedding": [[{"node": "Document RAG Tool", "type": "ai_embedding", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Ollama (Change Base URL)": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Delete Old Doc Records": {"main": [[{"node": "Delete Old Data Records", "type": "main", "index": 0}]]}, "Delete Old Data Records": {"main": [[{"node": "Insert Document Metadata", "type": "main", "index": 0}]]}, "Postgres PGVector Store": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Document RAG Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Determine Tool Type": {"main": [[], [{"node": "SearXNG", "type": "main", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Determine Tool Type", "type": "main", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Respond to Webhook": {"main": [[]]}, "SearXNG": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTML", "type": "main", "index": 0}]]}, "HTML": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "6bf3fdee-3cf8-4c13-8126-b2593f0625d9", "meta": {"templateCredsSetupCompleted": true, "instanceId": "73cb7a3e883df514bb47e8d1b34526d30e2abb8f56cd99f10d5948a1e11b25aa"}, "id": "ewOFFE4oBnpsv9hy", "tags": []}