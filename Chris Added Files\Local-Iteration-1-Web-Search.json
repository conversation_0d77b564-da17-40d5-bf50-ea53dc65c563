{"name": "AI Agent Mastery Local P1", "nodes": [{"parameters": {}, "id": "411f3d94-00e0-4f30-831d-cdf52fac1c36", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-980, 40], "notesInFlow": false, "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"content": "## RAG AI Agent with Cha<PERSON> Interface", "height": 525, "width": 1056}, "id": "976711dd-a9b2-457b-9b33-fb82727eb69a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1460, -320]}, {"parameters": {"options": {}}, "id": "d6c70eb0-de77-4b7e-95da-e77847c3ba8d", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-600, -220]}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId}}", "type": "string"}]}, "options": {}}, "id": "00d5e901-7dfd-4eeb-b4be-1298369f64e3", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1160, -220]}, {"parameters": {"public": true, "options": {}}, "id": "c19b9aa4-3d2b-41f6-a2d0-b9ade0e7c5df", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1420, -220], "webhookId": "e379f73b-9777-49d1-8ef2-7e6d652be35e"}, {"parameters": {"httpMethod": "POST", "path": "61bfb52b-66b1-4f2d-b9e6-e0acbfa2d9a3", "responseMode": "responseNode", "options": {}}, "id": "4e09f216-9319-4f3e-b5b2-6f151bfdb1a2", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1420, -20], "webhookId": "61bfb52b-66b1-4f2d-b9e6-e0acbfa2d9a3"}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are a personal assistant who helps answer questions from a corpus of documents. The documents are either text based (Txt, docs, extracted PDFs, etc.) or tabular data (CSVs or Excel documents).\n\nYou are given tools to perform RAG in the 'documents' table, look up the documents available in your knowledge base in the 'document_metadata' table, extract all the text from a given document, and query the tabular files with SQL in the 'document_rows' table.\n\nAlways start by performing RAG unless the users asks you to check a document or the question requires a SQL query for tabular data (fetching a sum, finding a max, something a RAG lookup would be unreliable for). If RAG doesn't help, then look at the documents that are available to you, find a few that you think would contain the answer, and then analyze those.\n\nAlways tell the user if you didn't find the answer. Don't make something up just to please them."}}, "id": "1fd97d0a-899d-4a6e-9c41-6b1ce58c1103", "name": "RAG AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-940, -220]}, {"parameters": {"model": {"__rl": true, "value": "qwen2.5:14b-8k", "mode": "list", "cachedResultName": "qwen2.5:14b-8k"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1140, 40], "id": "7ce313e3-413e-4d82-8715-7128d191e883", "name": "Ollama (Change Base URL)", "credentials": {"openAiApi": {"id": "yjDbkWOXNXC959ei", "name": "OpenAi account"}}}, {"parameters": {"content": "## Workflow Agent Tools", "height": 400, "width": 1680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1740, 220], "id": "db3d1801-24ad-40c4-aaf4-f5a556f05dce", "name": "Sticky Note7"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.tool_type }}", "rightValue": "image_analysis", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a50a5a5a-cc87-4654-97ea-05090efdb416", "leftValue": "={{ $json.tool_type }}", "rightValue": "web_search", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "none"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-1440, 360], "id": "7d23b341-cf68-4094-94c4-b7d1423a6520", "name": "Determine Tool Type"}, {"parameters": {"workflowInputs": {"values": [{"name": "tool_type"}, {"name": "query"}, {"name": "image_path"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1660, 360], "id": "108e381d-b7b7-41f7-8451-73853a4f67f1", "name": "Tool Start"}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.\n\nThis tool will return the contents of the 3 most relevant web pages from the search.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ $fromAI('query') }}", "tool_type": "web_search"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "image_path", "displayName": "image_path", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-820, 40], "id": "ea4ac74b-b4d7-4202-a9c2-dc2a49c6154b", "name": "Web Search Tool"}, {"parameters": {"url": "=http://searxng:8080/search?q={{ $json.query }}&format=json", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1200, 360], "id": "03186932-2a0f-4e76-8f6c-d2241c23851d", "name": "SearXNG"}, {"parameters": {"fieldToSplitOut": "results", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-1040, 360], "id": "9e65ca24-2f12-432d-a7b3-55872f533e34", "name": "Split Out"}, {"parameters": {"assignments": {"assignments": [{"id": "169ce734-0077-4c34-b7f1-40a35184fad6", "name": "url", "value": "={{ $json.url }}", "type": "string"}, {"id": "310e45f1-904e-4350-971f-a8519a49ab91", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "f6ac5cd2-4504-4f37-a766-33bc6ef09d47", "name": "content", "value": "={{ $json.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-880, 360], "id": "a0f21fc5-f6fe-44c6-a1ac-057976bd0804", "name": "Edit Fields2"}, {"parameters": {"maxItems": 3}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [-720, 360], "id": "00793d79-6d18-4fca-a382-fe507fb79969", "name": "Limit"}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "search_results", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-260, 360], "id": "d3d51c92-1877-4e86-83a3-d0acaeb63bdb", "name": "Aggregate1"}, {"parameters": {"url": "={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-560, 360], "id": "b4020ee4-8e52-4072-892a-d7f1d304cbd8", "name": "HTTP Request", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "site_html", "cssSelector": "body"}]}, "options": {}}, "type": "n8n-nodes-base.html", "typeVersion": 1.2, "position": [-420, 360], "id": "c3f9e2ea-be13-499f-a42d-684f38d3669c", "name": "HTML", "alwaysOutputData": true, "onError": "continueRegularOutput"}], "pinData": {"Tool Start": [{"json": {"tool_type": "web_search", "query": "Best AI Agent Frameworks", "tool_type 2": "image_analysis", "query 2": "Describe this image", "image_path": "/data/shared/ArchonMCPThumbnail.jpg"}}]}, "connections": {"Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Ollama (Change Base URL)": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Determine Tool Type": {"main": [[], [{"node": "SearXNG", "type": "main", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Determine Tool Type", "type": "main", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Respond to Webhook": {"main": [[]]}, "SearXNG": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTML", "type": "main", "index": 0}]]}, "HTML": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "9d566ad2-f2d9-4335-a58b-194d36ef1806", "meta": {"templateCredsSetupCompleted": true, "instanceId": "73cb7a3e883df514bb47e8d1b34526d30e2abb8f56cd99f10d5948a1e11b25aa"}, "id": "VUPgbZPkWANrHT9K", "tags": []}